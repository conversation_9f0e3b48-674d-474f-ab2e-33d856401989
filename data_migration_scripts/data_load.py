import pandas as pd
import os
import glob
import re
import datetime
import numpy as np
import argparse 
import logging

# create logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("data_processing.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


mapping_dict = {'96 morgan': '96 Morgan',
 '99 scott': '99 Scott',
 '99 scott (courtyard)': '99 Scott Courtyard',
 'ag': 'Avant Gardner',
 'avant gardner (great hall)': 'Avant Gardner',
 'big pink': 'Big Pink',
 'boat (circle line)': 'Circle Line Cruises',
 'brooklyn mirage': 'The Brooklyn Mirage',
 'brooklyn monarch': 'The Brooklyn Monarch',
 'brooklyn paramount': 'Brooklyn Paramount',
 'chocolate factory': 'The Chocolate Factory',
 'factory town': 'Factory Town',
 'great hall at avant gardner[pfyvlnf]': 'Avant Gardner',
 'h0l0': 'H0L0',
 'h0l0(1)': 'H0L0',
 'h0lo[_]': 'H0L0',
 'holo': 'H0L0',
 'irving plaza': 'Irving Plaza',
 'kdc': 'Knockdown Center',
 'kdc ruins': 'The Ruins at Knockdown Center',
 'knockdown center': 'Knockdown Center',
 'musica': 'Musica NYC',
 'musica (09/01/23)': 'Musica NYC',
 'musica pre-settlement': 'Musica NYC',
 'nectar': '96 Morgan',
 'quantum': 'Quantum Brooklyn',
 'quantum bk': 'Quantum Brooklyn',
 'quantum brooklyn': 'Quantum Brooklyn',
 'quantum[_]': 'Quantum Brooklyn',
 'quantum[_](1)': 'Quantum Brooklyn',
 'quantum[anbdi8n]': 'Quantum Brooklyn',
 'quantum[hiae6wb]': 'Quantum Brooklyn',
 'quantum[jo17g5s]': 'Quantum Brooklyn',
 'quantum[lpikvua]': 'Quantum Brooklyn',
 'quantum[ub1crcd]': 'Quantum Brooklyn',
 'quantum[v5drfy2]': 'Quantum Brooklyn',
 'quantum[ykkg8tv]': 'Quantum Brooklyn',
 'roof': 'The Roof at Superior Ingredients ',
 'roof/ room': 'The Roof at Superior Ingredients  | The Room at Superior Ingredients',
 'room': 'The Room at Superior Ingredients',
 'ruins knockdown center': 'The Ruins at Knockdown Center',
 'si-bk main room[_]': 'The Room at Superior Ingredients',
 'si-bk roof[_]': 'The Roof at Superior Ingredients',
 'si-bk roof': 'The Roof at Superior Ingredients',
 'si-bk room': 'The Room at Superior Ingredients',
 'the room si-bk': 'The Room at Superior Ingredients',
 'the roof si-bk': 'The Roof at Superior Ingredients',
 'main room si-bk': 'The Room at Superior Ingredients',
 'main roof si-bk': 'The Roof at Superior Ingredients',
 'si bk': 'The Roof at Superior Ingredients',
 'si-bk': 'The Roof at Superior Ingredients',
 'si-roof': 'The Roof at Superior Ingredients',
 'si-room': 'The Room at Superior Ingredients',
 'si roof': 'The Roof at Superior Ingredients ',
 'si room': 'The Room at Superior Ingredients',
 'superior ingredients': 'The Roof at Superior Ingredients',
 'superior ingredients (jbird)': 'J Bird at Superior Ingredients',
 'superior ingredients (roof)[7gzgb21]': 'The Roof at Superior Ingredients',
 'superior ingredients (roof)[m2qz7w7]': 'The Roof at Superior Ingredients',
 'superior ingredients roof': 'The Roof at Superior Ingredients ',
 'superior ingredients roof + main': 'The Roof at Superior Ingredients  | The Room at Superior Ingredients',
 'superior ingredients rooftop': 'The Roof at Superior Ingredients ',
 'superior ingredients room': 'The Room at Superior Ingredients',
 'superior ingredients[5vt4x6s]': 'The Room at Superior Ingredients',
 'soho live': 'Soho Live',
 'somewhere nowhere': 'Somewhere Nowhere',
 'soundcheck (dc)': 'Soundcheck',
 'sunbar': 'Sunbar Tempe',
 'toejam backlot': 'Toejam Backlot'}


def normalize_venue(raw_name: str) -> str:
    key = raw_name.strip().lower()
    return mapping_dict.get(key, raw_name)


def standardize_venue_name(venue):
    """Standardize venue names to handle variations"""
    # Convert to lowercase and remove extra spaces
    venue = venue.strip()
    
    # Remove spaces around hyphens
    venue = re.sub(r'\s*-\s*', '-', venue)
    
    # Replace multiple spaces with a single space
    venue = re.sub(r'\s+', ' ', venue)
    
    # Remove special characters 
    venue = re.sub(r'[$@#%&*!?]', '', venue)
    
    # Normalize venue name
    venue = normalize_venue(venue)
    
    return venue

def extract_artist_venue_date(file_path):
    """Extract artist, venue, and date information from the Excel file"""
    try:
        # Read the Overview sheet to get artist, venue, and date
        df = pd.read_excel(file_path, sheet_name="Overview", header=None)
        df = df.drop(0, axis=1)
        
        # Find the row with "Event" label
        idx = df[df[1].astype(str).str.strip().str.lower().isin(['event', 'event '])][2].index
        
        if len(idx) > 0:
            # Get artist and venue
            full = df.loc[idx[0]][2]
            artist = "Unknown Artist"
            venue = "UNKNOWN"
            
            # Handle both "@" and "at" as separators
            if '@' in full:
                artist, venue = full.split('@', 1)  # Split on first occurrence only
                artist = artist.strip()
                venue = venue.strip()
                venue = standardize_venue_name(venue)
            elif ' at ' in full.lower():
                parts = re.split(r'\s+at\s+', full, flags=re.IGNORECASE, maxsplit=1)
                if len(parts) == 2:
                    artist, venue = parts
                    artist = artist.strip()
                    venue = venue.strip()
                    venue = standardize_venue_name(venue)
            else:
                # If no separator found in the Event field, try to extract from filename
                filename = os.path.basename(file_path)
                filename_noext = os.path.splitext(filename)[0]
                logger.info(f"No separator in Event field '{full}', trying filename: {filename_noext}")
                
                if '@' in filename_noext:
                    logger.info(f"Found @ separator in filename: {filename_noext}")
                    artist, venue = filename_noext.split('@', 1)
                    artist = artist.strip()
                    venue = venue.strip()
                    # Удаляем дату в скобках из venue, если она там есть
                    venue = re.sub(r'\s*\(\d{2}_\d{2}_\d{2,4}\)\s*', '', venue)
                    venue = standardize_venue_name(venue)
                    logger.info(f"Extracted from filename - Artist: '{artist}', Venue: '{venue}'")
                elif ' at ' in filename_noext.lower():
                    logger.info(f"Found 'at' separator in filename: {filename_noext}")
                    parts = re.split(r'\s+at\s+', filename_noext, flags=re.IGNORECASE, maxsplit=1)
                    if len(parts) == 2:
                        artist, venue = parts
                        artist = artist.strip()
                        venue = venue.strip()
                        venue = re.sub(r'\s*\(\d{2}_\d{2}_\d{2,4}\)\s*', '', venue)
                        venue = standardize_venue_name(venue)
                        logger.info(f"Extracted from filename - Artist: '{artist}', Venue: '{venue}'")
                else:
                    # Use the full Event field value as artist
                    artist = str(full).strip() if full and str(full).strip() else "Unknown Artist"
                    logger.warning(f"No separator found, using Event field as artist: '{artist}'")
            
            
            # Get date - try multiple approaches
            idx_date = idx[0] + 1
            date = None
            
            # First attempt - direct cell value
            try:
                date_cell = df.loc[idx_date][2]
                if isinstance(date_cell, datetime.datetime):
                    date = date_cell
            except:
                pass
                
            # Second attempt - try to extract date from filename
            if date is None:
                try:
                    # Extract date pattern from filename like "(MM_DD_YY)" or "(MM_DD_YYYY)"
                    filename = os.path.basename(file_path)
                    date_match = re.search(r'\((\d{2})_(\d{2})_(\d{2,4})\)', filename)
                    if date_match:
                        month, day, year = date_match.groups()
                        if len(year) == 2:
                            year = '20' + year
                        date = datetime.datetime(int(year), int(month), int(day))
                except:
                    pass
            
            # Format date if found
            if date:
                # Check if the event is in the future and skip if it is
                today = datetime.datetime.now()
                if date > today:
                    logger.info(f"Skipping future event: {os.path.basename(file_path)} (Date: {date.strftime('%m/%d/%Y')})")
                    return None, None, None, True  # True indicates it's a future event
                
                date_formatted = f"{date.month}/{date.day}/{date.year}"
            else:
                # Last resort - use filename or mark as unknown
                try:
                    filename = os.path.basename(file_path)
                    date_formatted = f"Unknown Date from file: {filename}"
                except:
                    date_formatted = "Unknown Date"
            
            # Если venue осталось UNKNOWN, добавляем к нему название файла
            if venue == "UNKNOWN":
                filename_only = os.path.basename(file_path)
                venue = f"UNKNOWN {filename_only}"
            
            return artist, venue, date_formatted, False  # False indicates not a future event
        else:
            logger.warning(f"Event row not found in {os.path.basename(file_path)}")
            
            # Try extract inform from file
            filename = os.path.basename(file_path)
            filename_noext = os.path.splitext(filename)[0]
            artist = "Unknown Artist"
            venue = "UNKNOWN"
            date_formatted = "Unknown Date"
            
            #  Extract artist and venue from file name
            if '@' in filename_noext:
                logger.info(f"Found @ separator in filename: {filename_noext}")
                artist, venue = filename_noext.split('@', 1)
                artist = artist.strip()
                venue = venue.strip()
                venue = re.sub(r'\s*\(\d{2}_\d{2}_\d{2,4}\)\s*', '', venue)
                venue = standardize_venue_name(venue)
                logger.info(f"Extracted from filename - Artist: '{artist}', Venue: '{venue}'")
            elif ' at ' in filename_noext.lower():
                logger.info(f"Found 'at' separator in filename: {filename_noext}")
                parts = re.split(r'\s+at\s+', filename_noext, flags=re.IGNORECASE, maxsplit=1)
                if len(parts) == 2:
                    artist, venue = parts
                    artist = artist.strip()
                    venue = venue.strip()
                    venue = re.sub(r'\s*\(\d{2}_\d{2}_\d{2,4}\)\s*', '', venue)
                    venue = standardize_venue_name(venue)
                    logger.info(f"Extracted from filename - Artist: '{artist}', Venue: '{venue}'")
            else:
                logger.warning(f"No separator found in filename: {filename_noext}")
            
            # Extract date froma name of file
            date_match = re.search(r'\((\d{2})_(\d{2})_(\d{2,4})\)', filename)
            if date_match:
                month, day, year = date_match.groups()
                if len(year) == 2:
                    year = '20' + year
                
                try:
                    date = datetime.datetime(int(year), int(month), int(day))
                    logger.info(f"Extracted date from filename: {month}/{day}/{year}")
                    
                    # Check on the future
                    today = datetime.datetime.now()
                    if date > today:
                        logger.info(f"Skipping future event from filename: {filename} (Date: {date.strftime('%m/%d/%Y')})")
                        return None, None, None, True
                    
                    date_formatted = f"{date.month}/{date.day}/{date.year}"
                except ValueError as ve:
   
                    logger.warning(f"Invalid date values in filename {filename}: {ve}")
                    date_formatted = f"Unknown Date from file: {filename}"
            else:
                logger.warning(f"No date pattern found in filename: {filename}")
            

            if venue == "UNKNOWN":
                venue = f"UNKNOWN {filename}"
                logger.warning(f"Venue still unknown, using: {venue}")
            
            return artist, venue, date_formatted, False
    except Exception as e:
        logger.error(f"Error extracting artist, venue, date from {file_path}: {str(e)}")
        filename_only = os.path.basename(file_path)
        
        # if sheet name another or not available
        try:
            filename_noext = os.path.splitext(filename_only)[0]
            artist = "Unknown Artist"
            venue = "UNKNOWN"
            date_formatted = "Unknown Date"
 
            if '@' in filename_noext:
                logger.info(f"Exception handler: Found @ separator in filename: {filename_noext}")
                artist, venue = filename_noext.split('@', 1)
                artist = artist.strip()
                venue = venue.strip()
                venue = re.sub(r'\s*\(\d{2}_\d{2}_\d{2,4}\)\s*', '', venue)
                venue = standardize_venue_name(venue)
                logger.info(f"Exception handler: Extracted - Artist: '{artist}', Venue: '{venue}'")
            elif ' at ' in filename_noext.lower():
                logger.info(f"Exception handler: Found 'at' separator in filename: {filename_noext}")
                parts = re.split(r'\s+at\s+', filename_noext, flags=re.IGNORECASE, maxsplit=1)
                if len(parts) == 2:
                    artist, venue = parts
                    artist = artist.strip()
                    venue = venue.strip()
                    venue = re.sub(r'\s*\(\d{2}_\d{2}_\d{2,4}\)\s*', '', venue)
                    venue = standardize_venue_name(venue)
                    logger.info(f"Exception handler: Extracted - Artist: '{artist}', Venue: '{venue}'")
            
            date_match = re.search(r'\((\d{2})_(\d{2})_(\d{2,4})\)', filename_only)
            if date_match:
                month, day, year = date_match.groups()
                if len(year) == 2:
                    year = '20' + year
                
                try:
                    date = datetime.datetime(int(year), int(month), int(day))
                    
                    today = datetime.datetime.now()
                    if date > today:
                        logger.info(f"Exception handler: Skipping future event from filename: {filename_only} (Date: {date.strftime('%m/%d/%Y')})")
                        return None, None, None, True
                    
                    date_formatted = f"{date.month}/{date.day}/{date.year}"
                    logger.info(f"Exception handler: Extracted date from filename: {date_formatted}")
                except ValueError as ve:
                    logger.warning(f"Exception handler: Invalid date values in filename {filename_only}: {ve}")
                    date_formatted = f"Unknown Date from file: {filename}"
            
            if venue == "UNKNOWN":
                venue = f"UNKNOWN {filename_only}"
                logger.warning(f"Exception handler: Venue still unknown, using: {venue}")
            
            return artist, venue, date_formatted, False
        except Exception as nested_e:
            logger.error(f"Nested error in exception handler for {file_path}: {str(nested_e)}")
            return "Unknown Artist", f"UNKNOWN {filename_only}", "Unknown Date", False

def process_expenses(file_path, artist, venue, date_formatted):
    """Process the Expenses sheet and extract relevant data"""
    try:
        df = pd.read_excel(file_path, sheet_name="Overview", header=None)
        df = df.drop(0, axis=1)
        
        # Find the Expenses section
        expenses_idx = df[df[1].astype(str).str.strip().str.lower() == 'expenses'].index
        
        if len(expenses_idx) > 0:
            exp_table_idx = expenses_idx[0] + 1
            
            # Get column names
            new_columns = df.loc[exp_table_idx].tolist()

            # Create new DataFrame with data below
            df_new = df.iloc[exp_table_idx+1:].copy()
            df_new.columns = new_columns

            # Get relevant columns
            df_slice = df_new.loc[:, 'Date':'Status']

            # Add venue, artist, and date columns
            df_slice['Venue'] = venue
            df_slice['Event Date'] = date_formatted
            df_slice['Artist'] = artist

            # Reorder columns
            special = ["Venue", "Event Date", "Artist"]
            others = [c for c in df_slice.columns if c not in special]
            new_order = special + others
            df_slice = df_slice[new_order]

            # Filter for confirmed expenses and format amounts
            raw_expenses = df_slice[df_slice['Status'].astype(str).str.strip().str.lower() == 'confirmed'].drop('Status', axis=1)
            raw_expenses['Amount'] = raw_expenses['Amount'].apply(
                lambda x: f"${x:,.2f}" if pd.notna(x) else ""
            )

            return raw_expenses
        else:
            logger.info(f"No 'Expenses' section found in {os.path.basename(file_path)}")
            return pd.DataFrame()
    except Exception as e:
        logger.error(f"Error processing expenses from {file_path}: {str(e)}")
        return pd.DataFrame()

def process_additional_revenue(file_path, artist, venue, date_formatted):
    """Process the Additional Revenue section and extract relevant data"""
    try:
       
        sheet_names_to_try = ["Settlement (normal)", "Settlement"]
        df = None
        
        for sheet_name in sheet_names_to_try:
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
                logger.info(f"Successfully read sheet '{sheet_name}' from {os.path.basename(file_path)}")
                break
            except Exception as e:
                if sheet_name == sheet_names_to_try[-1]:  # If this was the last sheet name to try
                    logger.error(f"Error: Could not find any of the settlement sheets in {os.path.basename(file_path)}")
                    return pd.DataFrame()
                continue
        
        if df is None:
            return pd.DataFrame()
            
        df = df.drop(0, axis=1)
        
        # Find Additional Revenue section
        ar_sections = df[df[1].astype(str).str.strip().str.lower().isin(['additional revenue', 'additional revenue ', 'additional revenue'])][1].index
        
        if len(ar_sections) > 0:
            ar_idx = ar_sections[0]
            idx_ar_table = ar_idx + 1
            
            new_columns_ar_table = df.loc[idx_ar_table].tolist()
            
            # Create new DataFrame with data below
            df_new_ar = df.iloc[idx_ar_table+1:].copy()
            df_new_ar.columns = new_columns_ar_table
            
            # Get relevant columns
            df_slice_ar = df_new_ar.loc[:, 'Item':'Entity'].dropna(how='all')
            
            # Find the end of the table (marked by "Expenses" row with empty Amount and Entity)
            mask = (
                (df_slice_ar['Item'] == 'Expenses') &
                (df_slice_ar['Amount'].isna()) &
                (df_slice_ar['Entity'].isna())
            )
            
            if any(mask):
                idx_mask = df_slice_ar[mask].index[0]
                # Slice to this row exclusively
                df_slice_ar = df_slice_ar.loc[:idx_mask-1]
            
            # Add venue, artist, and date columns
            df_slice_ar['Venue'] = venue
            df_slice_ar['Event Date'] = date_formatted
            df_slice_ar['Artist'] = artist
            
            # Reorder columns
            special_ar = ["Venue", "Event Date", "Artist"]
            others_ar = [c for c in df_slice_ar.columns if c not in special_ar]
            new_order_ar = special_ar + others_ar
            df_slice_ar = df_slice_ar[new_order_ar]
            
            # Format amounts
            df_slice_ar['Amount'] = df_slice_ar['Amount'].apply(
                lambda x: f"${x:,.2f}" if pd.notna(x) else ""
            )
            
            return df_slice_ar
        else:
            logger.info(f"No 'Additional Revenue' section found in {os.path.basename(file_path)}")
            return pd.DataFrame()
    except Exception as e:
        logger.error(f"Error processing additional revenue from {file_path}: {str(e)}")
        return pd.DataFrame()

def get_facility_fee(file_path):
    """Get facility fee information from the Excel file"""
    try:
        df = pd.read_excel(file_path, sheet_name="Overview", header=None)
        df = df.drop(0, axis=1)
        
        # Search for facility fee using lowercase and stripped values
        values = ['FAC FEE', 'FAC FEES', 'FF']
        values_low = [v.lower() for v in values]
        
        # Normalize all cells to lowercase strings without spaces
        # Fix deprecated applymap with alternative approach
        df_norm = df.astype(str)
        for col in df_norm.columns:
            df_norm[col] = df_norm[col].map(lambda x: x.strip().lower())
        
        # Find matches
        mask_ff = df_norm.isin(values_low)
        matches = mask_ff.stack()[mask_ff.stack()]
        coords = matches.index.tolist()
        
        if coords:
            # ff_coord = [k for i in coords for k in i]
            i, j = coords[0]
            price_ff = df.iloc[i, j]
            name_ff = df.iloc[i, j-1]
            
            # Convert price_ff to numeric, handling "-" and other non-numeric values
            if isinstance(price_ff, str):
                price_ff_clean = price_ff.replace('$', '').replace(',', '').strip()
                if price_ff_clean == '-' or price_ff_clean == '':
                    price_ff = 0
                else:
                    try:
                        price_ff = float(price_ff_clean)
                    except ValueError:
                        price_ff = 0
            
            return name_ff, price_ff
        else:
            return None, None
    except Exception as e:
        logger.error(f"Error getting facility fee from {file_path}: {str(e)}")
        return None, None

def process_ticket_breakdown(file_path, artist, venue, date_formatted):
    """Process the Ticket Breakdown section and extract relevant data"""
    try:
        # Get facility fee information
        name_ff, price_ff = get_facility_fee(file_path)
        
        # Try different sheet names - first "Settlement (normal)", then just "Settlement"
        sheet_names_to_try = ["Settlement (normal)", "Settlement"]
        df = None
        
        for sheet_name in sheet_names_to_try:
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
                logger.info(f"Successfully read sheet '{sheet_name}' from {os.path.basename(file_path)}")
                break
            except Exception as e:
                if sheet_name == sheet_names_to_try[-1]:  # If this was the last sheet name to try
                    logger.error(f"Error: Could not find any of the settlement sheets in {os.path.basename(file_path)}")
                    return pd.DataFrame()
                continue
        
        if df is None:
            return pd.DataFrame()
            
        df = df.drop(0, axis=1)
        
        # Find Ticket Breakdown section
        tb_sections = df[df[1].astype(str).str.strip().str.lower().isin(
            ['ticket breakdown', 'ticket breakdown ', 'ticket breakdown']
        )][1].index
        
        if len(tb_sections) > 0:
            tb_idx = tb_sections[0]
            idx_tb_table = tb_idx + 1
            
            new_columns_tb_table = df.loc[idx_tb_table].tolist()
            
            # Create new DataFrame with data below
            df_new_tb = df.iloc[idx_tb_table+1:].copy()
            df_new_tb.columns = new_columns_tb_table
            
            # Get relevant columns
            df_slice_tb = df_new_tb.loc[:, 'Tier':'Entity'].dropna(how='all')
            
            # Find the end of the table (marked by "Total" row)
            mask_tb = df_slice_tb['Tier'] == 'Total'
            if any(mask_tb):
                idx_mask_tb = df_slice_tb[mask_tb].index[0]
                # Slice to this row exclusively
                df_slice_tb = df_slice_tb.loc[:idx_mask_tb-1].dropna(how='all')
            
            # Reset index for appending facility fee
            df_slice_tb = df_slice_tb.reset_index(drop=True)
            
            # Add facility fee if available
            if name_ff is not None and price_ff is not None:
                next_idx = len(df_slice_tb)
                df_slice_tb.loc[next_idx] = {'Tier': name_ff, 'Revenue': price_ff, 'Entity': 'Seers'}
            
            # Add venue, artist, and date columns
            df_slice_tb['Venue'] = venue
            df_slice_tb['Event Date'] = date_formatted
            df_slice_tb['Artist'] = artist
            
            
            if 'Fees' not in df_slice_tb.columns:
                df_slice_tb['Fees'] = ""
            
            # Reorder columns
            special_tb = ["Venue", "Event Date", "Artist"]
            others_tb = [c for c in df_slice_tb.columns if c not in special_tb]
            new_order_tb = special_tb + others_tb
            df_slice_tb = df_slice_tb[new_order_tb]
            
            # Format numerical columns
            for col in ['Price', 'Revenue']:
                if col in df_slice_tb.columns:
                    df_slice_tb[col] = df_slice_tb[col].map(lambda x: f"${x:,.2f}" if pd.notna(x) else "")
            
            return df_slice_tb
        else:
            logger.info(f"No 'Ticket Breakdown' section found in {os.path.basename(file_path)}")
            return pd.DataFrame()
    except Exception as e:
        logger.error(f"Error processing ticket breakdown from {file_path}: {str(e)}")
        return pd.DataFrame()

def save_dataframes_to_excel(all_dataframes):
    """Save all collected DataFrames to ALL.xlsx file"""
    # Create folder if it doesn't exist
    folder = 'venues_data'
    os.makedirs(folder, exist_ok=True)
    
    # Use single file for all venues
    filename = os.path.join(folder, "ALL.xlsx")

    # Log stats before saving
    for sheet_name, df in all_dataframes.items():
        if not df.empty:
            logger.info(f"Total rows in {sheet_name}: {len(df)}")

    # Check if file already exists
    if os.path.exists(filename):
        # Read and merge with existing data for each sheet
        with pd.ExcelWriter(filename, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            for sheet_name, df in all_dataframes.items():
                if not df.empty:
                    try:
                        # Try to read existing sheet
                        existing = pd.read_excel(filename, sheet_name=sheet_name)
                        
                        # Combine data
                        combined = pd.concat([existing, df], ignore_index=True)
                        
                        # Sort by date if appropriate column exists
                        date_column = None
                        if 'Event Date' in combined.columns:
                            date_column = 'Event Date'
                        elif 'Date' in combined.columns and sheet_name != "RAW EXPENSES":
                            date_column = 'Date'
                            
                        if date_column:
                            # Convert dates to datetime for sorting
                            combined['Date_Sort'] = pd.to_datetime(combined[date_column], errors='coerce')
                            combined = combined.sort_values(by='Date_Sort')
                            combined = combined.drop('Date_Sort', axis=1)
                        
                        combined.to_excel(writer, sheet_name=sheet_name, index=False)
                    except ValueError:
                        # Sheet doesn't exist, just write the dataframe
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
    else:
        # File doesn't exist - create it with all sheets
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            for sheet_name, df in all_dataframes.items():
                if not df.empty:
                    # Sort by date if appropriate column exists
                    date_column = None
                    if 'Event Date' in df.columns:
                        date_column = 'Event Date'
                    elif 'Date' in df.columns and sheet_name != "RAW EXPENSES":
                        date_column = 'Date'
                        
                    if date_column:
                        # Convert dates to datetime for sorting
                        df['Date_Sort'] = pd.to_datetime(df[date_column], errors='coerce')
                        df = df.sort_values(by='Date_Sort')
                        df = df.drop('Date_Sort', axis=1)
                    
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    logger.info(f"All data saved to {filename}")

def process_output_data(file_path, artist, venue, date_formatted, expenses_df, additional_rev_df, ticket_breakdown_df, facility_fee_info):
    """Process data and create an OUTPUT sheet with summary information"""
    try:
        # Initialize variables with default values
        seers_ticket_revenue = 0
        seers_nonticket_revenue = 0
        seers_talent_expense = 0
        seers_staffing_expense = 0
        seers_marketing_expense = 0
        seers_insurance_expense = 0
        venue_non_ticket_revenue = 0
        venue_venue_expense = 0
        tb_sold_tkts = 0

        # Extract facility fee (set to 0 if None)
        name_ff, price_ff = facility_fee_info if facility_fee_info[0] is not None else (None, 0)
        fac_fees = price_ff if price_ff is not None else 0
        
        # Process ticket revenue if available
        if not ticket_breakdown_df.empty:
            raw_tb = ticket_breakdown_df.copy()
            # Convert revenue to numeric values
            raw_tb['Revenue'] = raw_tb['Revenue'].str.replace(r'[\$,]', '', regex=True)
            # Replace "-" and other non-numeric values with 0
            raw_tb['Revenue'] = pd.to_numeric(raw_tb['Revenue'], errors='coerce').fillna(0)
            
            fac_fee_keywords = ['fac fee', 'fac fees', 'ff']
            not_fac_fee_mask = ~raw_tb['Tier'].astype(str).str.lower().isin(fac_fee_keywords)
            
            # Also check if Tier contains these words as part of the text
            for keyword in fac_fee_keywords:
                not_fac_fee_mask = not_fac_fee_mask & ~raw_tb['Tier'].astype(str).str.lower().str.contains(keyword)
            
            # Filter by Seers (case insensitive) and exclude FAC FEES rows
            seers_rows = raw_tb[(raw_tb['Entity'].str.lower() == 'seers') & not_fac_fee_mask]
            if not seers_rows.empty:
                seers_ticket_revenue = seers_rows['Revenue'].sum()
            
            # Get count of sold tickets
            tb_sold_tkts = pd.to_numeric(raw_tb['Sold'], errors='coerce').sum()


        # Process additional revenue if available
        if not additional_rev_df.empty:
            raw_add_rev = additional_rev_df.copy()
            raw_add_rev['Amount'] = raw_add_rev['Amount'].str.replace(r'[\$,]', '', regex=True)
            # Replace "-" and other non-numeric values with 0
            raw_add_rev['Amount'] = pd.to_numeric(raw_add_rev['Amount'], errors='coerce').fillna(0)
            
            # Filter by Seers and sum
            seers_rows = raw_add_rev[raw_add_rev['Entity'].str.lower() == 'seers']
            if not seers_rows.empty:
                seers_nonticket_revenue = seers_rows['Amount'].sum()
            
            # Get venue non-ticket revenue
            venue_rows = raw_add_rev[raw_add_rev['Entity'].str.lower() == 'venue']
            if not venue_rows.empty:
                venue_non_ticket_revenue = venue_rows['Amount'].sum()
        
        # Process expenses if available
        if not expenses_df.empty:
            raw_expenses = expenses_df.copy()
            raw_expenses['Amount'] = raw_expenses['Amount'].str.replace(r'[\$,]', '', regex=True)
            # Replace "-" and other non-numeric values with 0
            raw_expenses['Amount'] = pd.to_numeric(raw_expenses['Amount'], errors='coerce').fillna(0)
            
            # Group by Entity and Category and calculate sum
            result = raw_expenses.groupby(["Entity", "Category"])["Amount"].sum().reset_index()
            
            # Safely extract expense values if they exist
            if not result.empty:
                seers_filter = (result['Entity'].str.lower() == "seers")
                
                talent_filter = seers_filter & (result['Category'] == "Talent")
                if any(talent_filter):
                    seers_talent_expense = -result[talent_filter]["Amount"].values[0]
                    
                staffing_filter = seers_filter & (result['Category'] == "Staffing")
                if any(staffing_filter):
                    seers_staffing_expense = -result[staffing_filter]["Amount"].values[0]
                    
                marketing_filter = seers_filter & (result['Category'] == "Marketing")
                if any(marketing_filter):
                    seers_marketing_expense = -result[marketing_filter]["Amount"].values[0]
                    
                insurance_filter = seers_filter & (result['Category'] == "Insurance")
                if any(insurance_filter):
                    seers_insurance_expense = -result[insurance_filter]["Amount"].values[0]
            
            # Get venue expenses
            venue_expenses = raw_expenses[raw_expenses["Entity"].str.lower() == "venue"]
            if not venue_expenses.empty:
                grouped_venue = venue_expenses.groupby(["Entity", "Category"])["Amount"].sum().reset_index()
                venue_filter = (grouped_venue['Entity'].str.lower() == "venue") & (grouped_venue['Category'] == "Venue")
                if any(venue_filter):
                    venue_venue_expense = -grouped_venue[venue_filter]["Amount"].values[0]
        
        # Calculate deal profit base 
        deal_profit_base = (seers_ticket_revenue + seers_nonticket_revenue + 
                           seers_talent_expense + seers_staffing_expense + 
                           seers_marketing_expense + seers_insurance_expense + 
                           venue_non_ticket_revenue + venue_venue_expense)
        
        # Get profit split percentage
        deal_profit_split = None  # Default value if not found
        try:
            
            sheet_names_to_try = ["Settlement (normal)", "Settlement"]
            df = None
            
            for sheet_name in sheet_names_to_try:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
                    break
                except Exception:
                    continue
            
            if df is not None:
                df = df.drop(0, axis=1)
                # Find profit split percentage using numpy
                coords = np.argwhere(df.values == "Profit Split %")
                if len(coords) == 0:
                    raise ValueError("no cells found with 'Profit Split %'")

                row_ps, col_ps = coords[0]
                row_next = row_ps + 1
                cell_next = str(df.iat[row_next, col_ps]) 
                if "seers" in cell_next.lower():
                    deal_profit_split = df.iat[row_next, col_ps + 1]
                else:
                    deal_profit_split = None  # Default to None if not found
        except Exception as e:
            logger.warning(f"Error getting profit split from {file_path}: {str(e)}")
            deal_profit_split = None  # Default to None if error occurs
        
        # Use default value for calculation if deal_profit_split is None
        calculation_split = 1 if deal_profit_split is None else deal_profit_split
        
        # Calculate gray area net income - здесь добавляем fac_fees
        gray_area_net = deal_profit_base * calculation_split + fac_fees
        
        # Create output DataFrame
        output_data = pd.DataFrame([{
            'Venue': venue,
            'Date': date_formatted,
            'Artist': artist,
            'Sold_Tickets': tb_sold_tkts,
            'Seers_Ticket_Revenue': seers_ticket_revenue,
            'Seers_NonTicket_Revenue': seers_nonticket_revenue,
            'Seers_Talent_Expense': seers_talent_expense,
            'Seers_Staffing_Expense': seers_staffing_expense,
            'Seers_Marketing_Expense': seers_marketing_expense,
            'Seers_Insurance_Expense': seers_insurance_expense,
            'Venue_NonTicket_Revenue': venue_non_ticket_revenue,
            'Venue_Venue_Expense': venue_venue_expense,
            'Deal_Profit_Base': deal_profit_base,
            'Deal_Profit_Split': deal_profit_split,
            'Seers_FACFees_Revenue': fac_fees,
            'Gray_Area_Net_Income': gray_area_net,
        }])
        
        # Format output data
        output_formatted_data = output_data.copy()
        for col in output_formatted_data.columns:
            if output_formatted_data[col].dtype in ['int64', 'float64'] and col != 'Deal_Profit_Split' and col != 'Sold_Tickets':
                output_formatted_data[col] = output_formatted_data[col].apply(lambda x: f"${x:,.2f}")
        
        # Percentage formatting for split
        output_formatted_data['Deal_Profit_Split'] = output_formatted_data['Deal_Profit_Split'].apply(
            lambda x: f"{x:.0%}" if pd.notna(x) else "100%"
        )
        
        return output_formatted_data
    except Exception as e:
        logger.error(f"Error processing output data from {file_path}: {str(e)}")
        # Even in case of error, return an empty DataFrame with the correct structure
        columns = ['Venue', 'Date', 'Artist', 'Seers_Ticket_Revenue', 'Seers_NonTicket_Revenue', 
                  'Seers_Talent_Expense', 'Seers_Staffing_Expense', 'Seers_Marketing_Expense', 
                  'Seers_Insurance_Expense', 'Venue_NonTicket_Revenue', 'Venue_Venue_Expense', 
                  'Deal_Profit_Base', 'Deal_Profit_Split', 'Seers_FACFees_Revenue', 'Gray_Area_Net_Income']
        return pd.DataFrame(columns=columns)

def process_excel_file(file_path, all_dfs, stats):
    """Process a single Excel file and extract data"""
    try:
        logger.info(f"Processing {os.path.basename(file_path)}...")
        
        # Extract artist, venue, and date
        artist, venue, date_formatted, is_future_event = extract_artist_venue_date(file_path)
        
        # Skip if this is a future event (extract_artist_venue_date returned is_future_event=True)
        if is_future_event:
            stats["future_events"] += 1
            return True  # Return True to count as successful but skip processing
        
        # Process expenses
        expenses_df = process_expenses(file_path, artist, venue, date_formatted)
        if not expenses_df.empty:
            all_dfs["RAW EXPENSES"] = pd.concat([all_dfs["RAW EXPENSES"], expenses_df], ignore_index=True)
            stats["expenses_processed"] += 1
        
        # Process additional revenue
        additional_rev_df = process_additional_revenue(file_path, artist, venue, date_formatted)
        if not additional_rev_df.empty:
            all_dfs["RAW ADDITIONAL REVENUES"] = pd.concat([all_dfs["RAW ADDITIONAL REVENUES"], additional_rev_df], ignore_index=True)
            stats["revenues_processed"] += 1
        
        # Get facility fee information
        facility_fee_info = get_facility_fee(file_path)
        if facility_fee_info[0] is None:
            facility_fee_info = (None, 0)
        else:
            stats["facility_fees_found"] += 1
        
        # Process ticket breakdown
        ticket_breakdown_df = process_ticket_breakdown(file_path, artist, venue, date_formatted)
        if not ticket_breakdown_df.empty:
            all_dfs["RAW TICKET REVENUES"] = pd.concat([all_dfs["RAW TICKET REVENUES"], ticket_breakdown_df], ignore_index=True)
            stats["ticket_breakdowns_processed"] += 1
        
        # Process output data - always call this function
        output_df = process_output_data(file_path, artist, venue, date_formatted, 
                                      expenses_df, additional_rev_df, ticket_breakdown_df, 
                                      facility_fee_info)
        if not output_df.empty:
            all_dfs["OUTPUT"] = pd.concat([all_dfs["OUTPUT"], output_df], ignore_index=True)
            stats["output_processed"] += 1
            logger.info(f"Added OUTPUT for {artist} @ {venue}")
        
        return True
    except Exception as e:
        logger.error(f"Error processing {file_path}: {str(e)}")
        stats["errors"] += 1
        return False

def main():
    """Main function to process all Excel files in the shows directory"""
    # Create argument parser
    parser = argparse.ArgumentParser(description='Process Excel files with event data.')
    parser.add_argument('--folder', '-f', default='shows')
    args = parser.parse_args()
    
    shows_dir = args.folder
    files = glob.glob(os.path.join(shows_dir, '*.xlsx'))
    
    if not files:
        logger.warning(f"No Excel files found in {shows_dir} directory.")
        return
    
    # Initialize DataFrames for each sheet
    all_dataframes = {
        "RAW EXPENSES": pd.DataFrame(),
        "RAW ADDITIONAL REVENUES": pd.DataFrame(),
        "RAW TICKET REVENUES": pd.DataFrame(),
        "OUTPUT": pd.DataFrame()
    }
    
    # Статистика для отслеживания
    stats = {
        "total_files": len(files),
        "processed_files": 0,
        "failed_files": 0,
        "future_events": 0,
        "expenses_processed": 0,
        "revenues_processed": 0,
        "ticket_breakdowns_processed": 0,
        "facility_fees_found": 0,
        "output_processed": 0,
        "errors": 0
    }
    
    for file_path in files:
        if process_excel_file(file_path, all_dataframes, stats):
            stats["processed_files"] += 1
            logger.info(f"Files processed success: {stats['processed_files']}")
        else:
            stats["failed_files"] += 1
            logger.info(f"Files failed: {stats['failed_files']}")
    
    # Save all collected data to Excel file
    save_dataframes_to_excel(all_dataframes)
    
    # Вывод статистики
    logger.info("\n--- Processing Statistics ---")
    logger.info(f"Total files: {stats['total_files']}")
    logger.info(f"Successfully processed: {stats['processed_files']}")
    logger.info(f"Failed: {stats['failed_files']}")
    logger.info(f"Future events skipped: {stats['future_events']}")
    logger.info(f"Files with processed expenses: {stats['expenses_processed']}")
    logger.info(f"Files with processed additional revenues: {stats['revenues_processed']}")
    logger.info(f"Files with processed ticket breakdowns: {stats['ticket_breakdowns_processed']}")
    logger.info(f"Files with facility fees: {stats['facility_fees_found']}")
    logger.info(f"Files with OUTPUT records: {stats['output_processed']}")
    logger.info(f"Total errors encountered: {stats['errors']}")
    
    print(f"\nProcessing complete: {stats['processed_files']} files processed successfully, {stats['failed_files']} failures.")
    print(f"OUTPUT records created: {stats['output_processed']}")
    if stats['output_processed'] < stats['processed_files'] - stats['future_events']:
        print(f"Note: {stats['processed_files'] - stats['future_events'] - stats['output_processed']} files were processed but did not produce OUTPUT records.")

if __name__ == "__main__":
    main()