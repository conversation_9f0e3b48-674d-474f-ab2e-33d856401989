import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import tz from 'dayjs/plugin/timezone'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import type {
  Venue,
  Event as PayloadEvent,
  VenueHold as PayloadHold,
} from '@/payload-types'

dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)
dayjs.extend(utc)
dayjs.extend(tz)


export type TimedSlot = {
  start: string; end: string; status: string; title?: string; eventId?: number; holdId?: number;
}
export type AllDaySlot = { date: string; status: string; allDay: true; title?: string; eventId?: number; holdId?: number; }
export type Slot = TimedSlot | AllDaySlot


type SimpleEvent = Pick<PayloadEvent, 'id' | 'name' | 'startDate' | 'endDate'>

type SimpleHold = Pick<
  PayloadHold,
  'startDatetime' | 'endDatetime' | 'priorityNumber' | 'id'
>

type WeeklyHour = NonNullable<Venue['weeklyClosedHours']>[number]
type HolidayHour = NonNullable<Venue['holidayClosedHours']>[number]

const isAllDayWeekly = (h: WeeklyHour): h is WeeklyHour & { isAllDay: true } =>
  h.isAllDay === true
const isTimedWeekly = (h: WeeklyHour): h is WeeklyHour & { isAllDay: false } =>
  h.isAllDay === false

const isAllDayHoliday = (
  h: HolidayHour
): h is HolidayHour & { isAllDay: true } => h.isAllDay === true
const isTimedHoliday = (
  h: HolidayHour
): h is HolidayHour & { isAllDay: false } => h.isAllDay === false


export interface ComputeParams {
  weeklyClosedHours: NonNullable<Venue['weeklyClosedHours']>
  holidayClosedHours: NonNullable<Venue['holidayClosedHours']>
  defaultAvailabilityStatus: NonNullable<Venue['defaultAvailabilityStatus']>
  events: SimpleEvent[]
  holds: SimpleHold[]
  range: { from: Date; to: Date }
  timezone: string
}


const rank = (s: string) => {
  if (s === 'Closed') return 300   // highest
  if (s === 'Event') return 200
  if (s.startsWith('Hold ')) return 100 - Number(s.split(' ')[1] || 0)
  return 0
}

const pushTimed = (arr: Slot[], s: dayjs.Dayjs, e: dayjs.Dayjs, status: string) => {
  if (e.isSameOrBefore(s)) return
  arr.push({ start: s.utc().toISOString(), end: e.utc().toISOString(), status })
}

const pushAllDay = (arr: Slot[], d: dayjs.Dayjs, status = 'Closed') => {
  arr.push({ date: d.format('YYYY-MM-DD'), status, allDay: true })
}

const parseLocal = (base: dayjs.Dayjs, time: string, tzStr: string) =>
  dayjs.tz(`${base.format('YYYY-MM-DD')} ${time}`, 'YYYY-MM-DD HH:mm', tzStr)

const isTimedSlot = (s: Slot): s is TimedSlot => 'start' in s


export const computeAvailabilitySlots = (p: ComputeParams): Slot[] => {
  const {
    weeklyClosedHours,
    holidayClosedHours,
    events,
    holds,
    defaultAvailabilityStatus,
    range: { from, to },
    timezone,
  } = p

  const slots: Slot[] = []

  const eachDay: dayjs.Dayjs[] = []
  for (
    let d = dayjs.tz(from, timezone).startOf('day');
    d.isBefore(dayjs.tz(to, timezone).endOf('day'));
    d = d.add(1, 'day')
  ) {
    eachDay.push(d)
  }

  weeklyClosedHours.filter(isAllDayWeekly).forEach(h => {
    eachDay
      .filter(d => d.format('dddd') === h.dayOfWeek)
      .forEach(d => pushAllDay(slots, d))
  })

  holidayClosedHours.filter(isAllDayHoliday).forEach(h => {
    pushAllDay(slots, dayjs.tz(h.date, timezone))
  })

  weeklyClosedHours.filter(isTimedWeekly).forEach(h => {
    eachDay
      .filter(d => d.format('dddd') === h.dayOfWeek)
      .forEach(d => {
        const s = parseLocal(d, h.closeStartTime!, timezone)
        const e = parseLocal(d, h.closeEndTime!, timezone)
        pushTimed(slots, s, e.isAfter(s) ? e : e.add(1, 'day'), 'Closed')
      })
  })

  holidayClosedHours.filter(isTimedHoliday).forEach(h => {
    const base = dayjs.tz(h.date, timezone)
    const s = parseLocal(base, h.closeStartTime!, timezone)
    const e = parseLocal(base, h.closeEndTime!, timezone)
    pushTimed(slots, s, e.isAfter(s) ? e : e.add(1, 'day'), 'Closed')
  })

  events.forEach(ev => {
    slots.push({
      start: dayjs(ev.startDate).utc().toISOString(),
      end: dayjs(ev.endDate).utc().toISOString(),
      status: 'Event',
      title: ev.name,
      eventId: ev.id,
    })
  })


  holds.forEach(h => {
    if (h.id === 0) {
      pushTimed(
        slots,
        dayjs(h.startDatetime),
        dayjs(h.endDatetime),
        'Busy',
      )
    } else {
      slots.push({
        holdId: h.id,
        start: dayjs(h.startDatetime).toISOString(),
        end: dayjs(h.endDatetime).toISOString(),
        status: `Hold ${h.priorityNumber}`,
        title: `Hold ${h.priorityNumber}`,
      })
    }
  })

  type Edge = { t: number; isStart: boolean; status: string; meta?: Partial<TimedSlot> }
  const edges: Edge[] = []

  slots.forEach(s => {
    if (isTimedSlot(s)) {
      const meta = { title: s.title, eventId: s.eventId, holdId: s.holdId }
      edges.push({ t: dayjs(s.start).valueOf(), isStart: true, status: s.status, meta })
      edges.push({ t: dayjs(s.end).valueOf(), isStart: false, status: s.status })
    }
  })

  edges.sort((a, b) => (a.t !== b.t ? a.t - b.t : a.isStart ? -1 : 1))

  const line: TimedSlot[] = []
  let cursor = dayjs(from).valueOf()
  const active = new Map<string, { rank: number; meta: Partial<TimedSlot> }>()

  const emit = (until: number) => {
    if (until <= cursor) return
    const best = [...active.entries()]
      .sort(([, a], [, b]) => b.rank - a.rank)[0]

    const meta = best?.[1].meta ?? {}
    line.push({ start: dayjs(cursor).toISOString(), end: dayjs(until).toISOString(), status: best?.[0] ?? defaultAvailabilityStatus, ...meta })
    cursor = until
  }

  edges.forEach(e => {
    emit(e.t)
    if (e.isStart) active.set(e.status, { rank: rank(e.status), meta: e.meta! })
    else active.delete(e.status)
  })
  emit(dayjs(to).valueOf())



  const merged: TimedSlot[] = []

  const closedDays = new Set(
    slots
      .filter(
        (s): s is AllDaySlot => !isTimedSlot(s) && s.status === 'Closed'
      )
      .map(s => s.date)
  )

  for (const seg of line) {
    if (seg.status !== 'Unknown') {
      const last = merged.at(-1)
      if (last && last.status === seg.status && last.end === seg.start) {
        last.end = seg.end
      } else {
        merged.push({ ...seg })
      }
      continue
    }

    let queue: { start: dayjs.Dayjs; end: dayjs.Dayjs }[] = [
      { start: dayjs(seg.start), end: dayjs(seg.end) },
    ]

    closedDays.forEach(dStr => {
      const dayStart = dayjs.tz(dStr, timezone).startOf('day')
      const dayEnd = dayStart.add(1, 'day')

      const next: typeof queue = []

      for (const q of queue) {
        if (q.end.isSameOrBefore(dayStart) || q.start.isSameOrAfter(dayEnd)) {
          next.push(q)
          continue
        }
        if (q.start.isBefore(dayStart)) next.push({ start: q.start, end: dayStart })
        if (q.end.isAfter(dayEnd)) next.push({ start: dayEnd, end: q.end })
      }
      queue = next
    })

    queue.forEach(q =>
      merged.push({
        start: q.start.toISOString(),
        end: q.end.toISOString(),
        status: 'Unknown',
      })
    )
  }
  return [
    ...merged,
    ...slots.filter(s => !isTimedSlot(s)),
  ]
}
