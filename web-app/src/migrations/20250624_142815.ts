import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
  ALTER TABLE "venue_holds" ADD COLUMN "event_organizer_id" integer NOT NULL;
  ALTER TABLE "venue_holds" ADD CONSTRAINT "venue_holds_event_organizer_id_event_organizers_id_fk" FOREIGN KEY ("event_organizer_id") REFERENCES "public"."event_organizers"("id") ON DELETE set null ON UPDATE no action;
  CREATE INDEX "venue_holds_event_organizer_idx" ON "venue_holds" USING btree ("event_organizer_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
  ALTER TABLE "venue_holds" DROP CONSTRAINT "venue_holds_event_organizer_id_event_organizers_id_fk";
  
  DROP INDEX "venue_holds_event_organizer_idx";
  ALTER TABLE "venue_holds" DROP COLUMN "event_organizer_id";`)
}
