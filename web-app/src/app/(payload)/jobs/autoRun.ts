import type { JobsConfig } from 'payload'

export const autoRun: JobsConfig['autoRun'] = async payload => {
  await payload.jobs.queue({
    workflow: 'legacyDataSyncWorkflow',
    queue: 'legacy-sync',
    input: undefined,
  })

  await payload.jobs.queue({
    workflow: 'diceOrganizerDiscoveryWorkflow',
    queue: 'dice-discovery',
    input: undefined,
  })

  await payload.jobs.queue({
    workflow: 'publicDataSyncWorkflow',
    queue: 'public-data-sync',
    input: { platform: 'all' },
  })

  return [
    {
      queue: 'default',
      cron: '*/1 * * * *',
      limit: 30,
    },
  ]
}
