import { NextResponse } from 'next/server'
import type { PayloadRequest } from 'payload'
import { computeAvailabilitySlots } from '@/utilities/availability'     // util from your snippet
import { getUserTenantIDs } from '@/utilities/getUserTenantIDs'

export async function getVenueAvailability(req: PayloadRequest) {
  const { venueId, start, end } = req.query as {
    venueId?: string
    start?: string
    end?: string
  }

  if (!venueId) {
    return NextResponse.json({ message: 'Missing venueId' }, { status: 400 })
  }

  const makeLocalDate = (s?: string) => {
    if (!s) return new Date()
    if (s.includes('T')) return new Date(s)
    const [y, m, d] = s.split('-').map(Number)
    return new Date(y!, m! - 1, d)
  }

  const from = makeLocalDate(start)
  const to   = end ? makeLocalDate(end) : new Date(from.getTime() + 7 * 864e5)

  const venue = await req.payload.findByID({
    collection: 'venues',
    id: venueId,
    depth: 0,
  })
  if (!venue) {
    return NextResponse.json({ message: 'Venue not found' }, { status: 404 })
  }

  const weeklyClosedHours       = venue.weeklyClosedHours       ?? []
  const holidayClosedHours      = venue.holidayClosedHours      ?? []
  const defaultAvailability     = venue.defaultAvailabilityStatus ?? 'Unknown'
  const timezone                = venue.timezone ?? 'UTC'

  const viewerOrgs = getUserTenantIDs(req.user)

  const [evRes, holdRes] = await Promise.all([
    req.payload.find({
      collection: 'events',
      where: {
        'Location.venue': { equals: venueId },
        startDate      : { less_than_equal   : to.toISOString() },
        endDate        : { greater_than_equal: from.toISOString() },
      },
      depth: 0,
      limit: 1000,
    }),
    req.payload.find({
      collection: 'venue-holds',
      where: {
        venue         : { equals: venueId },
        startDatetime : { less_than_equal   : to.toISOString() },
        endDatetime   : { greater_than_equal: from.toISOString() },
      },
      depth: 0,
      limit: 1000,
    }),
  ])

  const events = evRes.docs.map(ev => ({
    id        : ev.id,
    name      : ev.name,
    startDate : ev.startDate,
    endDate   : ev.endDate,
  }))

  const holds = holdRes.docs.map(h => {
    let mine =  false
    if(req.user?.roles?.includes('super-admin')){
      mine = true
    }
    else{
      mine = viewerOrgs.includes(Number(h.eventOrganizer))
    }
    return mine
      ? {
          id            : h.id,
          startDatetime : h.startDatetime,
          endDatetime   : h.endDatetime,
          priorityNumber: h.priorityNumber,
        }
      : {
          id: 0,
          startDatetime : h.startDatetime,
          endDatetime   : h.endDatetime,
          priorityNumber: 99, 
          busy          : true,  
        }
  })

  const slots = computeAvailabilitySlots({
    weeklyClosedHours,
    holidayClosedHours,
    defaultAvailabilityStatus: defaultAvailability,
    events,
    holds,
    range: { from, to },
    timezone,
  })  

  return NextResponse.json({ slots, timezone })
}
