import config from '@payload-config'
import { NextRequest, NextResponse } from 'next/server'

import { getPayloadHMR } from '@payloadcms/next/utilities'

const WORKFLOWS = [
  'legacyDataSyncWorkflow',
  'diceOrganizerDiscoveryWorkflow',
  'diceOrganizerSyncWorkflow',
  'launchCampaignsWorkflow',
  'publicDataSyncWorkflow',
]

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayloadHMR({ config })
    const { workflow, queue = 'default' } = await request.json()

    if (!workflow || !WORKFLOWS.includes(workflow)) {
      return NextResponse.json({ error: 'Invalid workflow' }, { status: 400 })
    }

    const job = await payload.jobs.queue({
      workflow,
      queue,
      input: undefined,
    })

    return NextResponse.json({
      success: true,
      jobId: job.id,
    })
  } catch {
    return NextResponse.json({ success: false, error: 'Failed to start workflow' }, { status: 500 })
  }
}
