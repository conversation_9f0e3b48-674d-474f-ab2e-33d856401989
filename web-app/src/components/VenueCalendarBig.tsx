"use client"

import { useState, useEffect, useMemo } from 'react'
import {
  Calendar,
  dateFnsLocalizer,
  SlotInfo,
  View,
  Event as RBCEvent,
} from 'react-big-calendar'
import {
  format,
  parse,
  startOfDay,
  addDays,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
} from 'date-fns'
import { enUS } from 'date-fns/locale'
import 'react-big-calendar/lib/css/react-big-calendar.css'
import { useRouter } from 'next/navigation'
import { useDocumentInfo, useDocumentDrawer } from '@payloadcms/ui'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import tzPlugin from 'dayjs/plugin/timezone'

dayjs.extend(utc)
dayjs.extend(tzPlugin)

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay: (d: Date) => d.getDay(),
  locales: { 'en-US': enUS },
})

 interface Slot {
  id?: string
  start: string
  end: string
  priorityNumber?: number
  status: string
  title?: string
  eventId?: string
  holdId?: string
}
const COLORS: Record<string, string> = {
  Unknown: '#e5e7eb',
  Closed: '#ef4444',
  Busy   : '#d1d5db',
  Event: '#22c55e',
  'Hold 1': '#facc15',
  'Hold 2': '#f59e0b',
  'Hold 3': '#fb923c',
}

export default function VenueCalendarBig() {
  const { id } = useDocumentInfo()
  const venueId = String(id)
  const router = useRouter()

  const [date, setDate] = useState(new Date())
  const [view, setView] = useState<View>('month')
  const [range, setRange] = useState<{ start: Date; end: Date }>()
  const [slots, setSlots] = useState<Slot[]>([])
  const [drawerId, setDrawerId] = useState<number | undefined>()
  const [initialData, setInitialData] = useState<any>()

  const [DocumentDrawer,, { toggleDrawer, isDrawerOpen }] = useDocumentDrawer({
    collectionSlug: 'venue-holds',
    id: drawerId,
  })

  const computeRange = (d: Date, v: View): [Date, Date] => {
    if (v === 'day') return [startOfDay(d), addDays(startOfDay(d), 1)]
    if (v === 'week') return [startOfWeek(d, { weekStartsOn: 1 }), endOfWeek(d, { weekStartsOn: 1 })]
    return [startOfMonth(d), endOfMonth(d)]
  }

  useEffect(() => {
    const [from, to] = computeRange(date, view)
    setRange({ start: from, end: to })
  }, [date, view])

  useEffect(() => {
    if (!venueId || !range) return
    const qs = `venueId=${venueId}&start=${range.start.toISOString()}&end=${range.end.toISOString()}`
    fetch(`/api/venues/availability?${qs}`)
      .then(r => (r.ok ? r.json() : Promise.reject(r.statusText)))
      .then(d => setSlots(d.slots))
      .catch(console.error)
  }, [venueId, range])

  const events: RBCEvent[] = useMemo(
    () =>
      slots.map(s =>
        'date' in s && s.date
          ? {
              start: new Date(`${s.date}T00:00:00`),
              end: new Date(`${s.date}T23:59:59`),
              title: s.title || s.status,
              resource: s,
              allDay: true,
            }
          : {
              start: new Date(s.start),
              end: new Date(s.end),
              title: s.title || s.status,
              resource: s,
            },
      ),
    [slots],
  )

  useEffect(() => {
    if (drawerId && !isDrawerOpen) toggleDrawer()
  }, [drawerId, isDrawerOpen])

  useEffect(() => {
    if (!isDrawerOpen) {
      setDrawerId(undefined)
      setInitialData(undefined)
      setRange(r => (r ? { ...r } : r))
    }
  }, [isDrawerOpen])

  const openHoldDrawer = (id?: number, start?: Date, end?: Date) => {
    if (id) {
      setDrawerId(id)
      return
    }
    setInitialData({
      venue: Number(venueId),
      startDatetime: start?.toISOString(),
      endDatetime: end?.toISOString(),
      priorityNumber: 1,
    })
    setDrawerId(undefined)
    toggleDrawer()
  }

  const onNavigate = (d: Date, v: View) => {
    setDate(d)
    setView(v)
  }

  return (
    <>
      <Calendar
        localizer={localizer}
        date={date}
        view={view}
        showMultiDayTimes
        onNavigate={onNavigate}
        onView={v => onNavigate(date, v)}
        events={events}
        views={['month', 'week', 'agenda']}
        style={{ height: 650 }}
        eventPropGetter={ev => ({
          style: {
            backgroundColor: COLORS[(ev.resource as Slot).status] || COLORS.Unknown,
            borderRadius: 6,
            border: 'none',
            color: '#fff',
          },
        })}
        selectable
        onSelectSlot={(info: SlotInfo) => openHoldDrawer(undefined, info.start, info.end)}
        onSelectEvent={ev => {
          const slot = ev.resource as Slot
          if (slot.status === 'Busy') return 
          if (slot.status.startsWith('Hold') && slot.holdId) {
            openHoldDrawer(Number(slot.holdId))
            return
          }
          if (slot.status === 'Event' && slot.eventId) {
            router.push(`/admin/collections/events/${slot.eventId}`)
            return
          }
        }}
      />
      <DocumentDrawer initialData={initialData} />
    </>
  )
}
