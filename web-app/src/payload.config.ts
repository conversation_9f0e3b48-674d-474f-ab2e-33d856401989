import path from 'path'
import sharp from 'sharp'
import { fileURLToPath } from 'url'

import { BasePayload, buildConfig } from 'payload'

import { postgresAdapter } from '@payloadcms/db-postgres'
import { nodemailerAdapter } from '@payloadcms/email-nodemailer'
import { multiTenantPlugin } from '@payloadcms/plugin-multi-tenant'
import { s3Storage } from '@payloadcms/storage-s3'

import { defaultLexical } from '@/fields/defaultLexical'

import { isSuperAdmin } from './access/isSuperAdmin'
import { allocateCode } from './app/(payload)/actions/allocateCode'
import { test } from './app/(payload)/actions/test'
import { jobs } from './app/(payload)/jobs'
import { Agencies } from './collections/Agencies'
import { Agents } from './collections/Agents'
import { Articles } from './collections/Articles'
import { ArtistDeals } from './collections/ArtistDeals'
import { Artists } from './collections/Artists'
import { Authors } from './collections/Authors'
import { CampaignTemplates } from './collections/CampaignTemplates'
import { CampaignTransactions } from './collections/CampaignTransactions'
import { Campaigns } from './collections/Campaigns'
import { Countries } from './collections/Countries'
import { Documents } from './collections/Documents'
import { EventBrands } from './collections/EventBrands'
import { EventOrganizers } from './collections/EventOrganizers'
import { Events } from './collections/Events'
import { FanNotificationListners } from './collections/FanNotificationListners'
import { FanUsers } from './collections/FanUsers'
import { FestivalProfiles } from './collections/FestivalProfile'
import { Festivals } from './collections/Festivals'
import { Genres } from './collections/Genres'
import { Hubs } from './collections/Hubs'
import { Managers } from './collections/Managers'
import { ManagmentCompanies } from './collections/ManagmentCompanies'
import { Media } from './collections/Media'
import { OchoEpisodes } from './collections/OchoEpisodes'
import { Orders } from './collections/Orders'
import { Pages } from './collections/Pages'
import { Promotions } from './collections/Promotions'
import { Residencies } from './collections/Residencies'
import { TicketTypes } from './collections/TicketTypes'
import { Tickets } from './collections/Tickets'
import { Users } from './collections/Users'
import { VenueHolds } from './collections/VenueHolds'
import { Venues } from './collections/Venues'
import { seedSanityData } from './lib/sanity/seed-data'
import { migrations } from './migrations'
import { Config } from './payload-types'
import { plugins } from './plugins'
import { getServerSideURL } from './utilities/getURL'
import { getUserTenantIDs } from './utilities/getUserTenantIDs'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)
const collections = [
  Authors,
  Pages,
  Media,
  FanUsers,
  Users,
  Artists,
  Countries,
  Events,
  Articles,
  EventBrands,
  FestivalProfiles,
  Festivals,
  Genres,
  OchoEpisodes,
  Residencies,
  Venues,
  Hubs,
  Tickets,
  Orders,
  TicketTypes,
  EventOrganizers,
  ArtistDeals,
  Documents,
  Agencies,
  Agents,
  ManagmentCompanies,
  Managers,
  FanNotificationListners,
  Promotions,
  CampaignTemplates,
  Campaigns,
  VenueHolds,
  CampaignTransactions,
].sort((a, b) => a.slug.localeCompare(b.slug))

export default buildConfig({
  onInit: async (payload: BasePayload) => {
    console.log('Payload CMS is initializing...')

    const { seedCountries } = await import('./seeds/seedCountries')

    await seedCountries(payload)

    const shouldRunJobs =
      process.env.NODE_ENV === 'production' || process.env.ENABLE_JOBS === 'true'

    if (shouldRunJobs) {
      await payload.db.deleteMany({
        collection: 'payload-jobs',
        where: {},
      })
    }
  },
  endpoints: [
    {
      path: '/sanity/seed',
      method: 'get',
      handler: seedSanityData,
    },

    {
      path: '/test',
      method: 'get',
      handler: test,
    },
    {
      path: '/redeemCode/:fanId/:promotionId',
      method: 'get',
      handler: allocateCode,
    },
  ],
  admin: {
    components: {},
    importMap: {
      baseDir: path.resolve(dirname),
    },
    user: Users.slug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667,
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024,
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900,
        },
      ],
    },
  },
  editor: defaultLexical,
  db: postgresAdapter({
    prodMigrations: migrations,
    pool: {
      connectionString: process.env.DATABASE_URI || '',
    },
  }),
  collections: [...collections],
  cors: [getServerSideURL()].filter(Boolean),
  globals: [],
  plugins: [
    ...plugins,
    s3Storage({
      collections: {
        media: {
          prefix: 'media',
        },
      },
      bucket: process.env.S3_BUCKET || '',
      config: {
        forcePathStyle: true,
        credentials: {
          accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
        },
        region: process.env.S3_REGION || 'auto',
        endpoint: process.env.S3_ENDPOINT || '',
      },
    }),
    multiTenantPlugin<Config>({
      tenantsSlug: 'eventOrganizers',
      collections: {},
      tenantField: {
        access: {
          read: () => true,
          update: ({ req }) => {
            if (isSuperAdmin(req.user)) {
              return true
            }
            return getUserTenantIDs(req.user).length > 0
          },
        },
      },
      tenantsArrayField: {
        includeDefaultField: false,
      },
      userHasAccessToAllTenants: user => isSuperAdmin(user),
    }),
  ],
  secret: process.env.PAYLOAD_SECRET as string,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  email:
    process.env.NODE_ENV === 'production'
      ? nodemailerAdapter({
          defaultFromAddress: process.env.EMAIL_FROM || '',
          defaultFromName: process.env.EMAIL_FROM_NAME || '',
        })
      : undefined,
  jobs,
  hooks: {
    afterError: [
      async ({ error }) => {
        console.error('Error in Payload CMS:', error)
      },
    ],
  },
  graphQL: { disable: true },
})
