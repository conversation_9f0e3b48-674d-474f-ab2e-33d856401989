import { APIError, PayloadRequest, type CollectionConfig } from 'payload'
import { contributor } from '@/access/contributor'
import { anyone } from '@/access/anyone'
import { hasConflict, validateOpen } from '@/utilities/holds'
import { getUserTenantIDs } from '@/utilities/getUserTenantIDs'


const isOwnHold = (req: PayloadRequest) => {
  if(req.user?.roles?.includes('super-admin')) {
    return true
  }
  const organizerIDs = getUserTenantIDs(req.user)
  return {
    eventOrganizer: { in: organizerIDs },
  }
}

export const VenueHolds: CollectionConfig = {
  slug: 'venue-holds',
  access: {
    read: anyone,
    create: contributor,
    update: ({ req }) => isOwnHold(req),
    delete: ({ req }) => isOwnHold(req),
  },
  admin: {
    useAsTitle: 'venue',
  },
  fields: [
    {
      name: 'venue',
      type: 'relationship',
      relationTo: 'venues',
      required: true,
    },
    {
      name: 'startDatetime',
      type: 'date',
      required: true,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'endDatetime',
      type: 'date',
      required: true,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'priorityNumber',
      type: 'number',
      min: 1,
      required: true,
    },
    {
      name: 'eventOrganizer',
      type: 'relationship',
      relationTo: 'eventOrganizers',
      required: true,
    },
  ],
  hooks: {
    beforeChange: [
      async ({ req, data, operation }) => {
          const { venue, startDatetime, endDatetime, priorityNumber, eventOrganizer } = data
          const foundVenue = await req.payload.findByID({
            collection: 'venues',
            id: Number(venue),
            depth: 0,
          })
          if (!foundVenue) {
            throw new APIError('Venue not found', 404)
          }

          if (!validateOpen(foundVenue, startDatetime, startDatetime)) {
            throw new APIError('Outside open hours', 409)
          }

          const clash = await hasConflict({
            payload: req.payload,
            venueId: String(venue),
            startIso: startDatetime,
            endIso: endDatetime,
            priority: priorityNumber
          })
          if (clash) {
            throw new APIError(JSON.stringify(clash), 409)
          }
          return {
            venue: Number(venue),
            priorityNumber: priorityNumber,
            startDatetime: startDatetime,
            endDatetime: endDatetime,
            eventOrganizer
          }
      }
    ]
  }
}
