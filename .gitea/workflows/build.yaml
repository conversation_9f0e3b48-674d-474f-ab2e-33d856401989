name: Run Infrastructure

on:
  push:
    branches:
      - main
    paths:
      - 'web-app/**'

jobs:
  run-infra:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    env:
      APP_IMAGE_NAME: ${{ github.repository }}/app
      APP_IMAGE_TAG: ${{ github.sha }}
    steps:
      - name: Manual Git Checkout
        run: |
          git clone --depth=1 https://${{ vars.DEPLOYMENT_USER_USERNAME }}:${{ secrets.DEPLOYMENT_USER_PASSWORD }}@git.apexmediamasters.app/${{ github.repository }}.git .

      - name: Set up Node.js for Pulumi
        uses: actions/setup-node@v4
        with:
          node-version: "22"

      - name: Prune Docker to free up space
        run: |
          docker system prune -a --volumes --force

      - name: Clone Repository
        run: | 
          git clone "https://${{ vars.DEPLOYMENT_USER_USERNAME }}:${{ secrets.DEPLOYMENT_USER_PASSWORD }}@git.apexmediamasters.app/10x/common-app-infrastructure.git"
          # cd common-app-infrastructure
          # git checkout grey-area
          # cd ..
          mv common-app-infrastructure/payload-cms-app infra
      - name: Prep Pulumi
        uses: pnpm/action-setup@v4
        with:
          package_json_file: ./infra/package.json
          run_install: |
            - cwd: ./infra
              args: [--frozen-lockfile, --strict-peer-dependencies]
      
      - name: Pulumi build and deploy
        id: pulumi
        uses: pulumi/actions@v6
        env:
          AWS_ACCESS_KEY_ID: "${{ secrets.AWS_ACCESS_KEY_ID }}"
          AWS_SECRET_ACCESS_KEY: "${{ secrets.AWS_SECRET_ACCESS_KEY }}"
          PULUMI_CONFIG_PASSPHRASE: "${{ secrets.PULUMI_CONFIG_PASSPHRASE }}"
          PULUMI_K8S_AWAIT_ALL: false
          AWS_REGION: "auto"
          backupS3KeyId: "${{ secrets.AWS_ACCESS_KEY_ID }}"
          backupS3SecretAccessKey: "${{ secrets.AWS_SECRET_ACCESS_KEY }}"
          backupS3Endpoint: "${{ vars.S3_BACKUP_ENDPOINT }}"
          PULUMI_BACKEND_URL: "s3://${{ vars.PULUMI_BUCKET_NAME }}?endpoint=${{ vars.PULUMI_BUCKET_ENDPOINT }}&disableSSL=false&s3ForcePathStyle=true"
          BUILDKIT_PROGRESS: plain  # Better logging
          DOCKER_BUILD_ARGS: "--no-cache --force-rm" 
          backupS3BucketName: "${{ vars.S3_BACKUP_BUCKET }}"
          backupPgPath: "${{ vars.POSTGRES_BACKUP_PATH }}"
          VULTR_API_KEY: "${{ secrets.VULTR_API_KEY }}"
        with:
          command: up
          work-dir: ./infra
          cloud-url: "s3://${{ vars.PULUMI_BUCKET_NAME }}?endpoint=${{ vars.PULUMI_BUCKET_ENDPOINT }}&disableSSL=false&s3ForcePathStyle=true"
          stack-name: production
          upsert: true
          comment-on-summary: true
          config-map: |
            registryHostname:
              value: "${{ vars.REGISTRY_HOSTNAME }}"
            registryUsername:
              value: "${{ vars.DEPLOYMENT_USER_USERNAME }}"
            registryPassword:
              value: "${{ secrets.DEPLOYMENT_USER_PASSWORD }}"
            appImageName:
              value: "${{ env.APP_IMAGE_NAME }}"
            appImageTag:
              value: "${{ env.APP_IMAGE_TAG }}"
            domainName:
              value: "${{ vars.DOMAIN_NAME }}"
            rackspaceApiToken:
              value: "${{ secrets.RACKSPACE_API_TOKEN_PRD }}"
            AWS_ACCESS_KEY_ID:
              value: "${{ secrets.AWS_ACCESS_KEY_ID }}"
            AWS_SECRET_ACCESS_KEY:
              value: "${{ secrets.AWS_SECRET_ACCESS_KEY }}"
            S3_BACKUP_BUCKET:
              value: "${{ vars.S3_BACKUP_BUCKET }}"
            S3_BACKUP_ENDPOINT:
              value: "${{ vars.S3_BACKUP_ENDPOINT }}"
            backupS3Endpoint: 
              value: "${{ vars.S3_BACKUP_ENDPOINT }}"
            POSTGRES_BACKUP_PATH:
              value: "${{ vars.POSTGRES_BACKUP_PATH }}"
            backupPgPath: 
              value: "${{ vars.POSTGRES_BACKUP_PATH }}"
            objectStorageReplicas:
              value: "${{ vars.OBJECT_STORAGE_REPLICAS }}"
            backupS3KeyId: 
              value: "${{ secrets.AWS_ACCESS_KEY_ID }}"
            backupS3BucketName: 
              value: "${{ vars.S3_BACKUP_BUCKET }}"
            backupS3SecretAccessKey: 
              value: "${{ secrets.AWS_SECRET_ACCESS_KEY }}"
            extraObjectBuckets:
              value: "${{ vars.EXTRA_OBJECT_BUCKETS }}"
            CLUSTER_REGION: 
              value: "${{ vars.CLUSTER_REGION }}"
            pgVolumeSize:
              value: "${{ vars.PG_VOLUME_SIZE }}"
            CLUSTER_PLAN:
              value: "${{ vars.CLUSTER_PLAN }}"
            AIS_VOLUME_SIZE:
              value: "${{ vars.AIS_VOLUME_SIZE }}"
            extraEnv:
              value: |
                {
                  "DICE_PARTNER_API_TOKEN": "${{ secrets.DICE_PARTNER_API_TOKEN }}",
                  "SPOTIFY_CLIENT_ID": "${{ secrets.SPOTIFY_CLIENT_ID }}",
                  "SPOTIFY_CLIENT_SECRET": "${{ secrets.SPOTIFY_CLIENT_SECRET }}",
                  "QFLOW_USER": "${{ secrets.QFLOW_USER }}",
                  "QFLOW_PASS": "${{ secrets.QFLOW_PASS }}",
                  "GEMINI_API_KEY": "${{ secrets.GEMINI_API_KEY }}",
                  "YOUTUBE_API_KEY": "${{ secrets.YOUTUBE_API_KEY }}",
                  "PLANETSCALE_DATABASE_HOST" : "${{ vars.PLANETSCALE_DATABASE_HOST }}",
                  "PLANETSCALE_DATABASE_USERNAME" : "${{ vars.PLANETSCALE_DATABASE_USERNAME }}",
                  "PLANETSCALE_DATABASE_PASSWORD" : "${{ secrets.PLANETSCALE_DATABASE_PASSWORD }}",
                  "CHARTMETRIC_TOKEN" : "${{ secrets.CHARTMETRIC_TOKEN }}",
                  "GEOAPIFY_API_KEY" : "${{ secrets.GEOAPIFY_API_KEY }}",
                  "OPENOBSERVE_URL" : "${{ vars.OPENOBSERVE_URL }}",
                  "OPENOBSERVE_USERNAME" : "${{ vars.OPENOBSERVE_USERNAME }}",
                  "OPENOBSERVE_PASSWORD" : "${{ secrets.OPENOBSERVE_PASSWORD }}",
                  "NODE_ENV": "production",
                  "ENABLE_JOBS": "true"
                }
      - name: Pulumi Output
        run: echo "${{ steps.pulumi.outputs.frontendIp }}"